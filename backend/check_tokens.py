#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from tokens.models import Token
from users.models import User

def check_tokens():
    """检查数据库中的token状态"""
    print("=== Token 状态检查 ===")
    
    # 检查所有token
    tokens = Token.objects.all()
    print(f"总token数量: {tokens.count()}")
    
    for token in tokens:
        print(f"\nToken: {token.name} ({token.symbol})")
        print(f"  地址: {token.address}")
        print(f"  阶段: {token.phase}")
        print(f"  是否激活: {token.is_active}")
        print(f"  网络: {token.network}")
        print(f"  创建者: {token.creator}")
        print(f"  创建时间: {token.created_at}")
        print(f"  是否活跃: {token.is_active}")
    
    # 检查活跃token
    active_tokens = Token.objects.filter(is_active=True)
    print(f"\n=== 活跃Token数量: {active_tokens.count()} ===")
    
    # 检查非活跃token
    inactive_tokens = Token.objects.filter(is_active=False)
    print(f"\n=== 非活跃Token数量: {inactive_tokens.count()} ===")
    
    # 检查用户
    print(f"\n=== 用户数量: {User.objects.count()} ===")

if __name__ == '__main__':
    check_tokens() 