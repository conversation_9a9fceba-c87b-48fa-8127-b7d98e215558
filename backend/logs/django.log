Watching for file changes with StatReloader
Failed to initialize Web3 client for sepolia: Failed to connect to sepolia network at https://sepolia.infura.io/v3/YOUR_INFURA_KEY
Connected to xlayer network at https://rpc.xlayer.tech
Connected to sepolia network at https://eth-sepolia.g.alchemy.com/v2/Dwhp-JulbzNpZrEHruaBSD7RRx4Eeukb
Connected to xlayer network at https://rpc.xlayer.tech
Connected to sepolia network at https://eth-sepolia.g.alchemy.com/v2/Dwhp-JulbzNpZrEHruaBSD7RRx4Eeukb
Connected to xlayer network at https://rpc.xlayer.tech
Connected to sepolia network at https://eth-sepolia.g.alchemy.com/v2/Dwhp-JulbzNpZrEHruaBSD7RRx4Eeukb
Connected to xlayer network at https://rpc.xlayer.tech
Connected to sepolia network at https://eth-sepolia.g.alchemy.com/v2/Dwhp-JulbzNpZrEHruaBSD7RRx4Eeukb
Connected to xlayer network at https://rpc.xlayer.tech
Connected to sepolia network at https://eth-sepolia.g.alchemy.com/v2/Dwhp-JulbzNpZrEHruaBSD7RRx4Eeukb
Connected to xlayer network at https://testrpc.xlayer.tech
Connected to xlayer-testnet network at https://testrpc.xlayer.tech
Connected to xlayer-mainnet network at https://rpc.xlayer.tech
Connected to sepolia network at https://eth-sepolia.g.alchemy.com/v2/Dwhp-JulbzNpZrEHruaBSD7RRx4Eeukb
Connected to xlayer network at https://testrpc.xlayer.tech
Connected to xlayer-testnet network at https://testrpc.xlayer.tech
Connected to xlayer-mainnet network at https://rpc.xlayer.tech
Connected to sepolia network at https://eth-sepolia.g.alchemy.com/v2/Dwhp-JulbzNpZrEHruaBSD7RRx4Eeukb
Connected to xlayer network at https://testrpc.xlayer.tech
Connected to xlayer-testnet network at https://testrpc.xlayer.tech
Connected to xlayer-mainnet network at https://rpc.xlayer.tech
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4173
"GET /static/admin/css/login.css HTTP/1.1" 200 951
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
"GET /static/admin/css/base.css HTTP/1.1" 200 22120
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
"GET /static/admin/js/theme.js HTTP/1.1" 200 1653
"GET /static/admin/css/responsive.css HTTP/1.1" 200 16565
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2362
"POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 7792
"GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
"GET /admin/auth/user/ HTTP/1.1" 200 12651
"GET /static/admin/css/changelists.css HTTP/1.1" 200 6878
"GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
"GET /static/admin/js/core.js HTTP/1.1" 200 6208
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 9777
"GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
"GET /static/admin/js/actions.js HTTP/1.1" 200 8076
"GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
"GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
"GET /static/admin/img/search.svg HTTP/1.1" 200 458
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 285314
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 325171
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/js/filters.js HTTP/1.1" 200 978
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 10970
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/add/ HTTP/1.1" 200 26009
"GET /static/admin/css/forms.css HTTP/1.1" 200 8525
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/js/change_form.js HTTP/1.1" 200 606
"GET /static/admin/css/widgets.css HTTP/1.1" 200 11991
"GET /admin/tokens/token/ HTTP/1.1" 200 12282
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 10970
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/add/ HTTP/1.1" 200 26009
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /admin/tokens/platformconfig/add/ HTTP/1.1" 200 26554
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /admin/tokens/platformconfig/add/ HTTP/1.1" 302 0
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 14238
"GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/1/change/ HTTP/1.1" 200 26835
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"OPTIONS /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 0
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
/Users/<USER>/Project/launch/backend/tokens/urls.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/config/settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Not Found: /api/tokens/upload-info/
"GET /api/tokens/upload-info/ HTTP/1.1" 404 46
/Users/<USER>/Project/launch/backend/tokens/upload_views.py changed, reloading.
Watching for file changes with StatReloader
Not Found: /api/tokens/upload-info/
"GET /api/tokens/upload-info/ HTTP/1.1" 404 46
/Users/<USER>/Project/launch/backend/tokens/urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/tokens/upload-info/ HTTP/1.1" 200 200
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"GET /media/token_images/b7b96312e3044efabcffbe3919a1546e.jpg HTTP/1.1" 200 26235
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"OPTIONS /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 0
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"GET /media/token_images/c375eb94ca724f998a781887425de2b9.jpg HTTP/1.1" 200 26235
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"GET /media/token_images/bc054051deca45b7a75da8dcb041b29c.jpg HTTP/1.1" 200 26235
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"GET /media/token_images/e5188386624d43318ed15b10b60754b3.jpg HTTP/1.1" 200 26235
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"GET /media/token_images/f5dd9dcbf5dc43b48fb0c2b5645dbb3b.jpg HTTP/1.1" 200 26235
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
"GET /api/tokens/?network=sepolia&limit=50 HTTP/1.1" 200 145
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4173
"GET /static/admin/css/login.css HTTP/1.1" 200 951
"GET /static/admin/js/theme.js HTTP/1.1" 200 1653
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
"GET /static/admin/css/responsive.css HTTP/1.1" 200 16565
"GET /static/admin/css/base.css HTTP/1.1" 200 22120
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2531
"POST /admin/login/?next=/admin/ HTTP/1.1" 200 4341
"POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 8207
"GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
Internal Server Error: /admin/tokens/platformconfig/
Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/mysql/base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'tokens_platformconfig.graduation_threshold' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 719, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/views/decorators/cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/sites.py", line 246, in inner
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 2163, in changelist_view
    "selection_note": _("0 of %(cnt)s selected") % {"cnt": len(cl.result_list)},
                                                           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/query.py", line 366, in __len__
    self._fetch_all()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/mysql/base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'tokens_platformconfig.graduation_threshold' in 'field list'")
"GET /admin/tokens/platformconfig/ HTTP/1.1" 500 255771
Watching for file changes with StatReloader
"GET /admin/tokens/platformconfig/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/tokens/platformconfig/ HTTP/1.1" 200 4217
"POST /admin/login/?next=/admin/tokens/platformconfig/ HTTP/1.1" 302 0
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 13978
"GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
"GET /static/admin/js/core.js HTTP/1.1" 200 6208
"GET /static/admin/css/changelists.css HTTP/1.1" 200 6878
"GET /static/admin/js/actions.js HTTP/1.1" 200 8076
"GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 9777
"GET /static/admin/img/search.svg HTTP/1.1" 200 458
"GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
"GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 285314
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 325171
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/js/filters.js HTTP/1.1" 200 978
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 13978
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/1/change/ HTTP/1.1" 200 25916
"GET /static/admin/css/forms.css HTTP/1.1" 200 8525
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/js/change_form.js HTTP/1.1" 200 606
"GET /static/admin/css/widgets.css HTTP/1.1" 200 11991
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/urls.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/admin.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 14736
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 14736
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 14736
"GET /static/admin/css/base.css HTTP/1.1" 304 0
"GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
"GET /static/admin/css/responsive.css HTTP/1.1" 304 0
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
"GET /static/admin/css/changelists.css HTTP/1.1" 304 0
"GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
"GET /static/admin/js/theme.js HTTP/1.1" 304 0
"GET /static/admin/js/core.js HTTP/1.1" 304 0
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
"GET /static/admin/js/urlify.js HTTP/1.1" 304 0
"GET /static/admin/js/actions.js HTTP/1.1" 304 0
"GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
"GET /static/admin/img/search.svg HTTP/1.1" 304 0
"GET /static/admin/img/icon-no.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
"GET /static/admin/js/filters.js HTTP/1.1" 304 0
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 14736
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 14736
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/1/change/ HTTP/1.1" 200 25916
"GET /static/admin/css/forms.css HTTP/1.1" 304 0
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/js/change_form.js HTTP/1.1" 304 0
"GET /static/admin/css/widgets.css HTTP/1.1" 304 0
"GET /admin/tokens/token/ HTTP/1.1" 200 11392
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 14736
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 14736
"GET /admin/tokens/token/ HTTP/1.1" 200 11392
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
/Users/<USER>/Project/launch/backend/users/models.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/urls.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 304 0
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 304 0
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
"GET /admin/auth/ HTTP/1.1" 200 6117
"GET /static/admin/css/dashboard.css HTTP/1.1" 304 0
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
"GET /admin/ HTTP/1.1" 200 7792
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /static/admin/css/base.css HTTP/1.1" 304 0
"GET /static/admin/js/theme.js HTTP/1.1" 304 0
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
"GET /static/admin/css/changelists.css HTTP/1.1" 304 0
"GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
"GET /static/admin/css/responsive.css HTTP/1.1" 304 0
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
"GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
"GET /static/admin/js/urlify.js HTTP/1.1" 304 0
"GET /static/admin/js/actions.js HTTP/1.1" 304 0
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
"GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
"GET /static/admin/js/core.js HTTP/1.1" 304 0
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
"GET /static/admin/img/search.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
"GET /static/admin/js/filters.js HTTP/1.1" 304 0
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
/Users/<USER>/Project/launch/backend/config/urls.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 14736
"GET /static/admin/img/icon-no.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/ HTTP/1.1" 200 11392
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
- Broken pipe from ('127.0.0.1', 61418)
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
Forbidden: /api/users/******************************************/
"GET /api/users/******************************************/ HTTP/1.1" 403 58
Forbidden: /api/users/******************************************/
"GET /api/users/******************************************/ HTTP/1.1" 403 58
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
Forbidden: /api/users/******************************************/
"GET /api/users/******************************************/ HTTP/1.1" 403 58
Forbidden: /api/users/******************************************/
"GET /api/users/******************************************/ HTTP/1.1" 403 58
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /admin/tokens/token/ HTTP/1.1" 200 11392
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 14736
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/group/ HTTP/1.1" 200 9070
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
Forbidden: /api/users/******************************************/
"GET /api/users/******************************************/ HTTP/1.1" 403 58
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
Forbidden: /api/users/******************************************/
"GET /api/users/******************************************/ HTTP/1.1" 403 58
Forbidden: /api/users/******************************************/
"GET /api/users/******************************************/ HTTP/1.1" 403 58
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/users/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/users/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/users/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/users/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/users/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
Not Found: /api/users/******************************************/
"GET /api/users/******************************************/ HTTP/1.1" 404 45
Not Found: /api/users/******************************************/
"GET /api/users/******************************************/ HTTP/1.1" 404 45
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
Method Not Allowed: /api/users/auto_login/
"POST /api/users/auto_login/ HTTP/1.1" 405 41
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
Method Not Allowed: /api/users/auto_login/
Method Not Allowed: /api/users/auto_login/
"POST /api/users/auto_login/ HTTP/1.1" 405 41
"POST /api/users/auto_login/ HTTP/1.1" 405 41
Watching for file changes with StatReloader
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
Method Not Allowed: /api/users/auto_login/
Method Not Allowed: /api/users/auto_login/
"POST /api/users/auto_login/ HTTP/1.1" 405 41
"POST /api/users/auto_login/ HTTP/1.1" 405 41
/Users/<USER>/Project/launch/backend/users/urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
Method Not Allowed: /api/users/auto_login/
Method Not Allowed: /api/users/auto_login/
"POST /api/users/auto_login/ HTTP/1.1" 405 41
"POST /api/users/auto_login/ HTTP/1.1" 405 41
/Users/<USER>/Project/launch/backend/users/urls.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/users/auto_login/ HTTP/1.1" 201 431
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"POST /api/users/auto_login/ HTTP/1.1" 201 431
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/ HTTP/1.1" 200 11392
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
Not Found: /api/users/auth/auto-login/
"POST /api/users/auth/auto-login/ HTTP/1.1" 404 3689
Not Found: /api/users/0x1234567890123456789012345678901234567890/
"GET /api/users/0x1234567890123456789012345678901234567890/ HTTP/1.1" 404 45
Not Found: /api/users/0x1234567890123456789012345678901234567890/
"PATCH /api/users/0x1234567890123456789012345678901234567890/ HTTP/1.1" 404 45
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /admin/auth/user/ HTTP/1.1" 200 12650
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/ HTTP/1.1" 200 11392
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
/Users/<USER>/Project/launch/backend/users/admin.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 15594
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/ HTTP/1.1" 200 15526
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/2/change/ HTTP/1.1" 200 22887
"GET /static/admin/css/forms.css HTTP/1.1" 304 0
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/css/widgets.css HTTP/1.1" 304 0
"GET /static/admin/js/change_form.js HTTP/1.1" 304 0
"GET /admin/users/user/ HTTP/1.1" 200 15526
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?page=1&limit=20&phase=GRADUATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=GRADUATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /admin/users/user/ HTTP/1.1" 200 15526
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/ HTTP/1.1" 200 15526
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/2/change/ HTTP/1.1" 200 22887
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/ HTTP/1.1" 200 15526
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 201 431
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /admin/users/user/ HTTP/1.1" 200 16265
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=GRADUATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
/Users/<USER>/Project/launch/backend/config/settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"POST /api/users/auto_login/ HTTP/1.1" 201 431
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /admin/users/user/ HTTP/1.1" 200 17003
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /admin/tokens/token/ HTTP/1.1" 200 12250
"GET /static/admin/css/base.css HTTP/1.1" 304 0
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
"GET /static/admin/css/changelists.css HTTP/1.1" 304 0
"GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
"GET /static/admin/js/theme.js HTTP/1.1" 304 0
"GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
"GET /static/admin/css/responsive.css HTTP/1.1" 304 0
"GET /static/admin/js/core.js HTTP/1.1" 304 0
"GET /static/admin/js/urlify.js HTTP/1.1" 304 0
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
"GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
"GET /static/admin/js/actions.js HTTP/1.1" 304 0
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
"GET /static/admin/img/search.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
"GET /static/admin/js/filters.js HTTP/1.1" 304 0
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 304 0
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
"GET /admin/tokens/token/ HTTP/1.1" 200 12250
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=GRADUATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
Not Found: /api/tokens/0x9b5af144283ef8/
"GET /api/tokens/0x9b5af144283ef8/ HTTP/1.1" 404 46
Not Found: /api/tokens/0x9b5af144283ef8/
"GET /api/tokens/0x9b5af144283ef8/ HTTP/1.1" 404 46
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
Watching for file changes with StatReloader
Internal Server Error: /api/tokens/
"POST /api/tokens/ HTTP/1.1" 500 93
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/tokens/ HTTP/1.1" 201 273
"GET /admin/tokens/token/ HTTP/1.1" 200 15402
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 304 0
Internal Server Error: /admin/tokens/token/1/change/
Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 840, in get_form
    return modelform_factory(self.model, **defaults)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/forms/models.py", line 654, in modelform_factory
    return type(form)(class_name, (form,), form_class_attrs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/forms/models.py", line 334, in __new__
    raise FieldError(message)
django.core.exceptions.FieldError: Unknown field(s) (has_graduated, is_curve_trading_active, graduation_progress, holders, image, can_graduate) specified for Token

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 719, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/views/decorators/cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/sites.py", line 246, in inner
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 1987, in change_view
    return self.changeform_view(request, object_id, form_url, extra_context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 1840, in changeform_view
    return self._changeform_view(request, object_id, form_url, extra_context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 1878, in _changeform_view
    ModelForm = self.get_form(
                ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 842, in get_form
    raise FieldError(
django.core.exceptions.FieldError: Unknown field(s) (has_graduated, is_curve_trading_active, graduation_progress, holders, image, can_graduate) specified for Token. Check fields/fieldsets/exclude attributes of class TokenAdmin.
"GET /admin/tokens/token/1/change/ HTTP/1.1" 500 161770
Internal Server Error: /api/tokens/0x4ecca91405a14051a09053f1f61e5384/
"GET /api/tokens/0x4ecca91405a14051a09053f1f61e5384/ HTTP/1.1" 500 79
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 500 79
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=GRADUATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
"POST /api/users/auto_login/ HTTP/1.1" 200 434
Not Found: /api/tokens/0x3C44CdDdB6a900fa2b585dd299e03d12FA4293BC/
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x3C44CdDdB6a900fa2b585dd299e03d12FA4293BC/ HTTP/1.1" 404 46
Not Found: /api/tokens/0x3C44CdDdB6a900fa2b585dd299e03d12FA4293BC/
"GET /api/tokens/0x3C44CdDdB6a900fa2b585dd299e03d12FA4293BC/ HTTP/1.1" 404 46
Internal Server Error: /api/tokens/0x4ecca91405a14051a09053f1f61e5384/
"GET /api/tokens/0x4ecca91405a14051a09053f1f61e5384/ HTTP/1.1" 500 95
/Users/<USER>/Project/launch/backend/tokens/models.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/tokens/0x4ecca91405a14051a09053f1f61e5384/ HTTP/1.1" 200 975
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"OPTIONS /api/tokens/ HTTP/1.1" 200 0
"POST /api/tokens/ HTTP/1.1" 201 272
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x8a1a0d2c8f1948eebb2d6a4df92f1622/ HTTP/1.1" 200 1040
"GET /api/tokens/0x8a1a0d2c8f1948eebb2d6a4df92f1622/ HTTP/1.1" 200 1040
Internal Server Error: /admin/tokens/token/1/change/
Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 840, in get_form
    return modelform_factory(self.model, **defaults)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/forms/models.py", line 654, in modelform_factory
    return type(form)(class_name, (form,), form_class_attrs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/forms/models.py", line 334, in __new__
    raise FieldError(message)
django.core.exceptions.FieldError: Unknown field(s) (holders, has_graduated, image, is_curve_trading_active, can_graduate, graduation_progress) specified for Token

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 719, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/views/decorators/cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/sites.py", line 246, in inner
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 1987, in change_view
    return self.changeform_view(request, object_id, form_url, extra_context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 1840, in changeform_view
    return self._changeform_view(request, object_id, form_url, extra_context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 1878, in _changeform_view
    ModelForm = self.get_form(
                ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 842, in get_form
    raise FieldError(
django.core.exceptions.FieldError: Unknown field(s) (holders, has_graduated, image, is_curve_trading_active, can_graduate, graduation_progress) specified for Token. Check fields/fieldsets/exclude attributes of class TokenAdmin.
"GET /admin/tokens/token/1/change/ HTTP/1.1" 500 161907
"GET /admin/tokens/token/ HTTP/1.1" 200 16149
Internal Server Error: /admin/tokens/token/2/change/
Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 840, in get_form
    return modelform_factory(self.model, **defaults)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/forms/models.py", line 654, in modelform_factory
    return type(form)(class_name, (form,), form_class_attrs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/forms/models.py", line 334, in __new__
    raise FieldError(message)
django.core.exceptions.FieldError: Unknown field(s) (holders, has_graduated, image, is_curve_trading_active, can_graduate, graduation_progress) specified for Token

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 719, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/views/decorators/cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/sites.py", line 246, in inner
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 1987, in change_view
    return self.changeform_view(request, object_id, form_url, extra_context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 1840, in changeform_view
    return self._changeform_view(request, object_id, form_url, extra_context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 1878, in _changeform_view
    ModelForm = self.get_form(
                ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 842, in get_form
    raise FieldError(
django.core.exceptions.FieldError: Unknown field(s) (holders, has_graduated, image, is_curve_trading_active, can_graduate, graduation_progress) specified for Token. Check fields/fieldsets/exclude attributes of class TokenAdmin.
"GET /admin/tokens/token/2/change/ HTTP/1.1" 500 161767
/Users/<USER>/Project/launch/backend/tokens/admin.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/admin.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/admin.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/admin.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/admin.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/tokens/token/ HTTP/1.1" 200 16149
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/2/change/ HTTP/1.1" 200 36036
"GET /static/admin/css/forms.css HTTP/1.1" 304 0
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
"GET /static/admin/js/calendar.js HTTP/1.1" 200 9141
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
"GET /static/admin/css/widgets.css HTTP/1.1" 304 0
"GET /static/admin/js/change_form.js HTTP/1.1" 304 0
"GET /static/admin/img/icon-clock.svg HTTP/1.1" 200 677
"GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
"GET /admin/tokens/token/2/change/ HTTP/1.1" 200 36036
"GET /api/tokens/0x4ecca91405a14051a09053f1f61e5384/ HTTP/1.1" 200 1062
"GET /api/tokens/0x4ecca91405a14051a09053f1f61e5384/ HTTP/1.1" 200 1062
"GET /api/tokens/0x4ecca91405a14051a09053f1f61e5384/ HTTP/1.1" 200 1062
"GET /api/tokens/0x4ecca91405a14051a09053f1f61e5384/ HTTP/1.1" 200 1062
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/0x8a1a0d2c8f1948eebb2d6a4df92f1622/ HTTP/1.1" 200 1068
"GET /api/tokens/0x8a1a0d2c8f1948eebb2d6a4df92f1622/ HTTP/1.1" 200 1068
"GET /api/tokens/0x8a1a0d2c8f1948eebb2d6a4df92f1622/ HTTP/1.1" 200 1068
"GET /api/tokens/0x8a1a0d2c8f1948eebb2d6a4df92f1622/ HTTP/1.1" 200 1068
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x8a1a0d2c8f1948eebb2d6a4df92f1622/ HTTP/1.1" 200 1068
"GET /api/tokens/0x8a1a0d2c8f1948eebb2d6a4df92f1622/ HTTP/1.1" 200 1068
"GET /api/tokens/0x8a1a0d2c8f1948eebb2d6a4df92f1622/ HTTP/1.1" 200 1068
"GET /api/tokens/0x8a1a0d2c8f1948eebb2d6a4df92f1622/ HTTP/1.1" 200 1068
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/tokens/ HTTP/1.1" 201 274
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x7ba2a6b491d84705bdc1dd4eae38bece/ HTTP/1.1" 200 1005
"GET /api/tokens/0x7ba2a6b491d84705bdc1dd4eae38bece/ HTTP/1.1" 200 1005
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 500 79
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=GRADUATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 500 79
"GET /admin/tokens/token/ HTTP/1.1" 200 16899
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 15594
"GET /static/admin/img/icon-no.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /api/tokens/upload-image/ HTTP/1.1" 200 241
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/tokens/ HTTP/1.1" 201 273
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x6e0b631443614ff584abdb3f4754727a/ HTTP/1.1" 200 990
"GET /api/tokens/0x6e0b631443614ff584abdb3f4754727a/ HTTP/1.1" 200 990
"GET /media/token_images/08b221cdec6143d7bc8cc9a334908697.jpg HTTP/1.1" 200 26235
"GET /admin/tokens/token/ HTTP/1.1" 200 17647
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
Internal Server Error: /admin/tokens/token/
Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/mysql/base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table 'xlayer.transactions_transaction' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 719, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/views/decorators/cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/sites.py", line 246, in inner
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 2057, in changelist_view
    response = self.response_action(
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 1686, in response_action
    response = func(self, request, queryset)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/actions.py", line 39, in delete_selected
    ) = modeladmin.get_deleted_objects(queryset, request)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 2199, in get_deleted_objects
    return get_deleted_objects(objs, request, self.admin_site)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/utils.py", line 138, in get_deleted_objects
    collector.collect(objs)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/utils.py", line 205, in collect
    return super().collect(objs, source_attr=source_attr, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/deletion.py", line 343, in collect
    if getattr(on_delete, "lazy_sub_objs", False) or sub_objs:
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/query.py", line 398, in __bool__
    self._fetch_all()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/mysql/base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.ProgrammingError: (1146, "Table 'xlayer.transactions_transaction' doesn't exist")
"POST /admin/tokens/token/ HTTP/1.1" 500 302823
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/0x6e0b631443614ff584abdb3f4754727a/ HTTP/1.1" 200 990
"GET /api/tokens/0x6e0b631443614ff584abdb3f4754727a/ HTTP/1.1" 200 990
"GET /api/tokens/0x6e0b631443614ff584abdb3f4754727a/ HTTP/1.1" 200 990
"GET /api/tokens/0x6e0b631443614ff584abdb3f4754727a/ HTTP/1.1" 200 990
"GET /media/token_images/08b221cdec6143d7bc8cc9a334908697.jpg HTTP/1.1" 304 0
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
/Users/<USER>/Project/launch/backend/config/settings.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/tokens/token/ HTTP/1.1" 200 17647
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 15594
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/1/change/ HTTP/1.1" 200 26774
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /admin/tokens/platformconfig/1/change/ HTTP/1.1" 200 27167
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /admin/tokens/platformconfig/1/change/ HTTP/1.1" 302 0
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 15820
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/img/icon-yes.svg HTTP/1.1" 304 0
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"OPTIONS /api/tokens/ HTTP/1.1" 200 0
"POST /api/tokens/ HTTP/1.1" 201 272
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
Not Found: /api/tokens/0xde5ccef69023494b89c8cf44a21260a9e2ba77d56ec9a86bfd880e933f8cf7a6/
"GET /api/tokens/0xde5ccef69023494b89c8cf44a21260a9e2ba77d56ec9a86bfd880e933f8cf7a6/ HTTP/1.1" 404 46
Not Found: /api/tokens/0xde5ccef69023494b89c8cf44a21260a9e2ba77d56ec9a86bfd880e933f8cf7a6/
"GET /api/tokens/0xde5ccef69023494b89c8cf44a21260a9e2ba77d56ec9a86bfd880e933f8cf7a6/ HTTP/1.1" 404 46
"GET /admin/tokens/token/ HTTP/1.1" 200 18392
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/5/change/ HTTP/1.1" 200 35892
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /media/token_images/bc7d26c6f35d4a5db31338b8ff27d71b.jpg HTTP/1.1" 200 10237
"GET /admin/tokens/token/5/change/ HTTP/1.1" 200 35892
"GET /admin/tokens/token/ HTTP/1.1" 200 18392
"GET /admin/tokens/token/4/change/ HTTP/1.1" 200 35898
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /media/token_images/08b221cdec6143d7bc8cc9a334908697.jpg HTTP/1.1" 200 26235
"GET /admin/tokens/token/4/change/ HTTP/1.1" 200 35898
"GET /admin/tokens/token/ HTTP/1.1" 200 18392
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 500 79
/Users/<USER>/Project/launch/backend/tokens/models.py changed, reloading.
Watching for file changes with StatReloader
Internal Server Error: /admin/tokens/token/
Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/mysql/base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table 'xlayer.transactions_transaction' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 719, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/views/decorators/cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/sites.py", line 246, in inner
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 2057, in changelist_view
    response = self.response_action(
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 1686, in response_action
    response = func(self, request, queryset)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/actions.py", line 39, in delete_selected
    ) = modeladmin.get_deleted_objects(queryset, request)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 2199, in get_deleted_objects
    return get_deleted_objects(objs, request, self.admin_site)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/utils.py", line 138, in get_deleted_objects
    collector.collect(objs)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/utils.py", line 205, in collect
    return super().collect(objs, source_attr=source_attr, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/deletion.py", line 343, in collect
    if getattr(on_delete, "lazy_sub_objs", False) or sub_objs:
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/query.py", line 398, in __bool__
    self._fetch_all()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/mysql/base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.ProgrammingError: (1146, "Table 'xlayer.transactions_transaction' doesn't exist")
"POST /admin/tokens/token/ HTTP/1.1" 500 304712
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/tokens/ HTTP/1.1" 201 287
"GET /api/tokens/0x1234567890123456789012345678901234567890/ HTTP/1.1" 200 1019
"GET /admin/tokens/token/ HTTP/1.1" 200 19156
Internal Server Error: /admin/tokens/token/
Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/mysql/base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table 'xlayer.transactions_transaction' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 719, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/views/decorators/cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/sites.py", line 246, in inner
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 2057, in changelist_view
    response = self.response_action(
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 1686, in response_action
    response = func(self, request, queryset)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/actions.py", line 39, in delete_selected
    ) = modeladmin.get_deleted_objects(queryset, request)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 2199, in get_deleted_objects
    return get_deleted_objects(objs, request, self.admin_site)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/utils.py", line 138, in get_deleted_objects
    collector.collect(objs)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/utils.py", line 205, in collect
    return super().collect(objs, source_attr=source_attr, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/deletion.py", line 343, in collect
    if getattr(on_delete, "lazy_sub_objs", False) or sub_objs:
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/query.py", line 398, in __bool__
    self._fetch_all()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/db/backends/mysql/base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.ProgrammingError: (1146, "Table 'xlayer.transactions_transaction' doesn't exist")
"POST /admin/tokens/token/ HTTP/1.1" 500 304772
"GET /admin/tokens/token/ HTTP/1.1" 200 19156
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 500 79
"POST /admin/tokens/token/ HTTP/1.1" 200 10298
"GET /static/admin/js/cancel.js HTTP/1.1" 200 884
"POST /admin/tokens/token/ HTTP/1.1" 302 0
"GET /admin/tokens/token/ HTTP/1.1" 200 15538
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/1/change/ HTTP/1.1" 200 36146
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/tokens/ HTTP/1.1" 201 278
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0000000000000000000000000000000000000000/ HTTP/1.1" 200 1013
"GET /api/tokens/0x0000000000000000000000000000000000000000/ HTTP/1.1" 200 1013
"GET /media/token_images/a63437221a8d4648b7ec6170c4700654.jpg HTTP/1.1" 200 10237
"GET /admin/tokens/token/ HTTP/1.1" 200 16149
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/7/change/ HTTP/1.1" 200 35909
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/ HTTP/1.1" 200 17003
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/2/change/ HTTP/1.1" 200 22887
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/ HTTP/1.1" 200 16149
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/tokens/0x74bd9df1de901bb345e1ed90e42684945c020bb2/ HTTP/1.1" 200 1013
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/tokens/ HTTP/1.1" 201 274
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /media/token_images/19aae185c86e4861baacd31d41b52dc9.jpg HTTP/1.1" 200 10237
"GET /admin/tokens/token/ HTTP/1.1" 200 16887
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /media/token_images/19aae185c86e4861baacd31d41b52dc9.jpg HTTP/1.1" 304 0
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /media/token_images/19aae185c86e4861baacd31d41b52dc9.jpg HTTP/1.1" 304 0
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /media/token_images/19aae185c86e4861baacd31d41b52dc9.jpg HTTP/1.1" 304 0
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /admin/tokens/token/ HTTP/1.1" 200 16887
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 15582
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/1/change/ HTTP/1.1" 200 26864
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/ HTTP/1.1" 200 16887
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /media/token_images/19aae185c86e4861baacd31d41b52dc9.jpg HTTP/1.1" 304 0
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /admin/tokens/token/8/change/ HTTP/1.1" 200 35891
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /media/token_images/19aae185c86e4861baacd31d41b52dc9.jpg HTTP/1.1" 304 0
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
"GET /api/tokens/?page=1&limit=20&phase=GRADUATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
"GET /api/tokens/?page=1&limit=20&phase=GRADUATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /media/token_images/19aae185c86e4861baacd31d41b52dc9.jpg HTTP/1.1" 304 0
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /media/token_images/19aae185c86e4861baacd31d41b52dc9.jpg HTTP/1.1" 304 0
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /media/token_images/19aae185c86e4861baacd31d41b52dc9.jpg HTTP/1.1" 304 0
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
Watching for file changes with StatReloader
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"OPTIONS /api/tokens/ HTTP/1.1" 200 0
"POST /api/tokens/ HTTP/1.1" 201 272
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /admin/ HTTP/1.1" 200 10855
"GET /static/admin/css/base.css HTTP/1.1" 304 0
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
"GET /static/admin/css/dashboard.css HTTP/1.1" 304 0
"GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
"GET /static/admin/css/responsive.css HTTP/1.1" 304 0
"GET /static/admin/js/theme.js HTTP/1.1" 304 0
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
"GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 200 392
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2684
"GET /admin/tokens/token/ HTTP/1.1" 200 17622
"GET /static/admin/css/changelists.css HTTP/1.1" 304 0
"GET /static/admin/js/core.js HTTP/1.1" 304 0
"GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
"GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
"GET /static/admin/js/actions.js HTTP/1.1" 304 0
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
"GET /static/admin/js/urlify.js HTTP/1.1" 304 0
"GET /static/admin/img/search.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/js/filters.js HTTP/1.1" 304 0
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 304 0
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 304 0
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
"GET /admin/tokens/token/9/change/ HTTP/1.1" 200 35959
"GET /static/admin/css/forms.css HTTP/1.1" 304 0
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
"GET /static/admin/js/calendar.js HTTP/1.1" 304 0
"GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/js/change_form.js HTTP/1.1" 304 0
"GET /static/admin/css/widgets.css HTTP/1.1" 304 0
"GET /static/admin/img/icon-clock.svg HTTP/1.1" 304 0
"GET /static/admin/img/icon-calendar.svg HTTP/1.1" 304 0
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 15583
"GET /static/admin/img/icon-no.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/1/change/ HTTP/1.1" 200 26865
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /media/token_images/19aae185c86e4861baacd31d41b52dc9.jpg HTTP/1.1" 304 0
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
"GET /api/tokens/0x6e17f18070605a2d843ea541a74ab0d85fe0d653/ HTTP/1.1" 200 1013
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
Watching for file changes with StatReloader
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /api/tokens/0x0d0c144d269416ca2e4da581fa838e092ae3b026/ HTTP/1.1" 200 1007
"GET /media/token_images/19aae185c86e4861baacd31d41b52dc9.jpg HTTP/1.1" 304 0
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"OPTIONS /api/tokens/ HTTP/1.1" 200 0
"POST /api/tokens/ HTTP/1.1" 201 272
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/0x28a8886dd8059bdc21fbf49912d3cabab29b0bea/ HTTP/1.1" 200 1013
"GET /api/tokens/0x28a8886dd8059bdc21fbf49912d3cabab29b0bea/ HTTP/1.1" 200 1013
"GET /api/tokens/0x28a8886dd8059bdc21fbf49912d3cabab29b0bea/ HTTP/1.1" 200 1013
"GET /api/tokens/0x28a8886dd8059bdc21fbf49912d3cabab29b0bea/ HTTP/1.1" 200 1013
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /admin/tokens/platformconfig/1/change/ HTTP/1.1" 200 26864
"GET /admin/tokens/platformconfig/1/change/ HTTP/1.1" 200 26864
"GET /static/admin/css/base.css HTTP/1.1" 304 0
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
"GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
"GET /static/admin/css/responsive.css HTTP/1.1" 304 0
"GET /static/admin/js/theme.js HTTP/1.1" 304 0
"GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
"GET /static/admin/css/forms.css HTTP/1.1" 304 0
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
"GET /static/admin/js/core.js HTTP/1.1" 304 0
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
"GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
"GET /static/admin/js/actions.js HTTP/1.1" 304 0
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
"GET /static/admin/js/urlify.js HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/js/change_form.js HTTP/1.1" 304 0
"GET /static/admin/css/widgets.css HTTP/1.1" 304 0
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 15582
"GET /static/admin/css/changelists.css HTTP/1.1" 304 0
"GET /static/admin/img/icon-no.svg HTTP/1.1" 304 0
"GET /static/admin/img/search.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/js/filters.js HTTP/1.1" 304 0
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 304 0
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 304 0
"GET /admin/tokens/platformconfig/1/change/ HTTP/1.1" 200 26864
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"OPTIONS /api/users/auto_login/ HTTP/1.1" 200 0
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?page=1&limit=20&phase=GRADUATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
"GET /api/tokens/?page=1&limit=20&phase=GRADUATED&sort=volume_24h&order=desc HTTP/1.1" 200 145
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 500 79
Internal Server Error: /api/tokens/
"GET /api/tokens/?page=1&limit=20&phase=CREATED&sort=volume_24h&order=desc HTTP/1.1" 500 79
"GET /admin/tokens/token/ HTTP/1.1" 200 18358
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
"POST /admin/tokens/token/ HTTP/1.1" 200 10267
"GET /static/admin/js/cancel.js HTTP/1.1" 304 0
"POST /admin/tokens/token/ HTTP/1.1" 302 0
"GET /admin/tokens/token/ HTTP/1.1" 200 12386
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/img/icon-yes.svg HTTP/1.1" 304 0
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"POST /api/users/auto_login/ HTTP/1.1" 200 434
"GET /api/tokens/?limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /admin/auth/group/ HTTP/1.1" 200 9928
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/user/ HTTP/1.1" 200 13508
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/ HTTP/1.1" 200 17003
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/tokens/?limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
Not Found: /api/users/get-nonce/******************************************/
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 404 3799
"GET /api/tokens/?page=1&limit=20&phase=CURVE&sort=volume_24h&order=desc HTTP/1.1" 200 145
Not Found: /api/users/get-nonce/******************************************/
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 404 3799
/Users/<USER>/Project/launch/backend/users/views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/users/urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 12055
"GET /static/admin/css/dashboard.css HTTP/1.1" 304 0
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
"GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 304 0
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 15582
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/1/change/ HTTP/1.1" 200 26864
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/user/ HTTP/1.1" 200 13508
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/ HTTP/1.1" 200 17003
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"OPTIONS /api/users/login/ HTTP/1.1" 200 0
Method Not Allowed: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 405 41
Method Not Allowed: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 405 41
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Method Not Allowed: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 405 41
Method Not Allowed: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 405 41
/Users/<USER>/Project/launch/backend/users/urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Internal Server Error: /api/users/login/
Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/views/generic/base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Project/launch/backend/users/views.py", line 198, in login_with_signature
    if user.nonce != nonce:
       ^^^^^^^^^^
AttributeError: 'User' object has no attribute 'nonce'
"POST /api/users/login/ HTTP/1.1" 500 104342
Internal Server Error: /api/users/login/
Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/views/generic/base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Project/launch/backend/users/views.py", line 198, in login_with_signature
    if user.nonce != nonce:
       ^^^^^^^^^^
AttributeError: 'User' object has no attribute 'nonce'
"POST /api/users/login/ HTTP/1.1" 500 104342
/Users/<USER>/Project/launch/backend/users/models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 41
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 41
"GET /admin/users/user/ HTTP/1.1" 200 17003
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/ HTTP/1.1" 200 17003
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /admin/users/user/ HTTP/1.1" 200 9963
"POST /admin/users/user/ HTTP/1.1" 302 0
"GET /admin/users/user/ HTTP/1.1" 200 12583
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 41
"GET /admin/users/user/ HTTP/1.1" 200 15515
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/8/change/ HTTP/1.1" 200 22862
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 41
Watching for file changes with StatReloader
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 41
"GET /admin/users/user/ HTTP/1.1" 200 15515
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
"GET /admin/users/user/ HTTP/1.1" 200 15515
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/8/change/ HTTP/1.1" 200 22862
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/ HTTP/1.1" 200 15515
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/ HTTP/1.1" 200 15515
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/ HTTP/1.1" 200 15515
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/ HTTP/1.1" 200 15515
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/ HTTP/1.1" 200 12250
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/users/user/ HTTP/1.1" 200 15515
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/users/get-nonce/0x347AEd7Fc99b598ae03Ab8B52b5a67cE3A4f999e/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x347AEd7Fc99b598ae03Ab8B52b5a67cE3A4f999e/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
"GET /admin/users/user/ HTTP/1.1" 200 16243
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
"GET /admin/users/user/ HTTP/1.1" 200 16972
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 491
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 491
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 491
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 491
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 491
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 491
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 491
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 491
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
"POST /api/users/login/ HTTP/1.1" 200 491
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/urls.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"GET /media/token_images/08f6ea55e7f748fda30dae577b8b9e9c.jpg HTTP/1.1" 200 10237
"OPTIONS /api/tokens/ HTTP/1.1" 200 0
"POST /api/tokens/ HTTP/1.1" 201 429
"GET /api/tokens/config/ HTTP/1.1" 200 647
Bad Request: /api/tokens/
"POST /api/tokens/ HTTP/1.1" 400 72
"GET /admin/tokens/token/ HTTP/1.1" 200 15402
"GET /static/admin/css/base.css HTTP/1.1" 304 0
"GET /static/admin/css/responsive.css HTTP/1.1" 304 0
"GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
"GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
"GET /static/admin/js/core.js HTTP/1.1" 304 0
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
"GET /static/admin/js/urlify.js HTTP/1.1" 304 0
"GET /static/admin/js/theme.js HTTP/1.1" 304 0
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
"GET /static/admin/css/changelists.css HTTP/1.1" 304 0
"GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
"GET /static/admin/img/search.svg HTTP/1.1" 304 0
"GET /static/admin/js/actions.js HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 304 0
"GET /static/admin/js/filters.js HTTP/1.1" 304 0
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 304 0
"GET /admin/tokens/token/11/change/ HTTP/1.1" 200 35930
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
"GET /static/admin/js/calendar.js HTTP/1.1" 304 0
"GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 304 0
"GET /static/admin/css/forms.css HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/css/widgets.css HTTP/1.1" 304 0
"GET /static/admin/js/change_form.js HTTP/1.1" 304 0
"GET /static/admin/img/icon-calendar.svg HTTP/1.1" 304 0
"GET /static/admin/img/icon-clock.svg HTTP/1.1" 304 0
Not Found: /users/get-nonce/******************************************/
"GET /users/get-nonce/******************************************/ HTTP/1.1" 404 2828
Not Found: /users/get-nonce/******************************************/
"GET /users/get-nonce/******************************************/ HTTP/1.1" 404 2828
Not Found: /users/get-nonce/******************************************/
"GET /users/get-nonce/******************************************/ HTTP/1.1" 404 2828
Not Found: /users/get-nonce/******************************************/
"GET /users/get-nonce/******************************************/ HTTP/1.1" 404 2828
"GET /api/tokens/ HTTP/1.1" 200 145
"GET /api/tokens/ HTTP/1.1" 200 145
Not Found: /users/get-nonce/******************************************/
"GET /users/get-nonce/******************************************/ HTTP/1.1" 404 2828
Not Found: /users/get-nonce/******************************************/
"GET /users/get-nonce/******************************************/ HTTP/1.1" 404 2828
"GET /api/tokens/ HTTP/1.1" 200 145
Not Found: /users/get-nonce/******************************************/
"GET /users/get-nonce/******************************************/ HTTP/1.1" 404 2828
Not Found: /users/get-nonce/******************************************/
"GET /users/get-nonce/******************************************/ HTTP/1.1" 404 2828
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"POST /admin/tokens/token/11/change/ HTTP/1.1" 302 0
"GET /admin/tokens/token/ HTTP/1.1" 200 15620
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/img/icon-yes.svg HTTP/1.1" 304 0
Internal Server Error: /api/tokens/
"GET /api/tokens/ HTTP/1.1" 500 79
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"OPTIONS /api/users/login/ HTTP/1.1" 200 0
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
Internal Server Error: /api/tokens/
"GET /api/tokens/ HTTP/1.1" 500 79
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
Internal Server Error: /api/tokens/
"GET /api/tokens/ HTTP/1.1" 500 79
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
Internal Server Error: /api/tokens/
"GET /api/tokens/?show_inactive=false HTTP/1.1" 500 79
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 948
"GET /media/token_images/08f6ea55e7f748fda30dae577b8b9e9c.jpg HTTP/1.1" 304 0
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"POST /api/users/login/ HTTP/1.1" 200 489
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 948
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 948
"POST /api/users/login/ HTTP/1.1" 200 489
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/users/get-nonce/0x347AEd7Fc99b598ae03Ab8B52b5a67cE3A4f999e/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x347AEd7Fc99b598ae03Ab8B52b5a67cE3A4f999e/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x347AEd7Fc99b598ae03Ab8B52b5a67cE3A4f999e/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 948
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 948
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 948
"GET /api/users/get-nonce/0x347AEd7Fc99b598ae03Ab8B52b5a67cE3A4f999e/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x347AEd7Fc99b598ae03Ab8B52b5a67cE3A4f999e/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 948
"GET /api/users/get-nonce/0x347AEd7Fc99b598ae03Ab8B52b5a67cE3A4f999e/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x347AEd7Fc99b598ae03Ab8B52b5a67cE3A4f999e/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 948
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 948
"GET /media/token_images/08f6ea55e7f748fda30dae577b8b9e9c.jpg HTTP/1.1" 304 0
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 948
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 948
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 948
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 948
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 948
"GET /media/token_images/08f6ea55e7f748fda30dae577b8b9e9c.jpg HTTP/1.1" 304 0
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/tokens/ HTTP/1.1" 201 233
"GET /admin/tokens/token/ HTTP/1.1" 200 16139
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/tokens/0x6dc165B04121a2c5603ac991F14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/0x6dc165B04121a2c5603ac991F14559e563bb51b3/ HTTP/1.1" 200 988
"GET /media/token_images/2546d54134f84345921dc76bb82ec9fd.jpg HTTP/1.1" 200 26235
"GET /api/tokens/0x6dc165B04121a2c5603ac991F14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /media/token_images/2546d54134f84345921dc76bb82ec9fd.jpg HTTP/1.1" 304 0
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/ HTTP/1.1" 200 1029
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /media/token_images/08f6ea55e7f748fda30dae577b8b9e9c.jpg HTTP/1.1" 304 0
"GET /media/token_images/2546d54134f84345921dc76bb82ec9fd.jpg HTTP/1.1" 304 0
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /media/token_images/2546d54134f84345921dc76bb82ec9fd.jpg HTTP/1.1" 304 0
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/ HTTP/1.1" 200 1029
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /media/token_images/2546d54134f84345921dc76bb82ec9fd.jpg HTTP/1.1" 304 0
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /media/token_images/08f6ea55e7f748fda30dae577b8b9e9c.jpg HTTP/1.1" 304 0
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
/Users/<USER>/Project/launch/backend/tokens/urls.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/tokens/token/ HTTP/1.1" 200 16139
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/ HTTP/1.1" 200 1029
/Users/<USER>/Project/launch/backend/tokens/urls.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/urls.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /media/token_images/2546d54134f84345921dc76bb82ec9fd.jpg HTTP/1.1" 304 0
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/ HTTP/1.1" 200 1029
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/ HTTP/1.1" 200 1029
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/ HTTP/1.1" 200 1029
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 9058
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5201
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/ HTTP/1.1" 200 1029
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 9023
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5207
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5201
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/ HTTP/1.1" 200 1029
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 8986
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/ HTTP/1.1" 200 1029
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5203
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 8930
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5209
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 9083
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 8932
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5206
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/ HTTP/1.1" 200 1029
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5208
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 8933
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 8971
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5211
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5204
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 9087
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5207
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 9091
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 8991
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5207
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5202
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 9059
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 8973
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5201
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 9032
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5210
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 9040
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5206
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/ HTTP/1.1" 200 1029
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/price-history/ HTTP/1.1" 200 8990
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/transaction-history/ HTTP/1.1" 200 5207
"GET /api/tokens/0x3c64a011439d44a0a9d2ad8c2012f127/ HTTP/1.1" 200 1029
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /media/token_images/08f6ea55e7f748fda30dae577b8b9e9c.jpg HTTP/1.1" 304 0
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /media/token_images/2546d54134f84345921dc76bb82ec9fd.jpg HTTP/1.1" 304 0
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5205
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 200 8916
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5204
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 200 8928
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5205
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5207
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5202
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5206
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5207
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5206
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 200 9138
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5207
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5205
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5203
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5204
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5207
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5205
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 200 9057
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5204
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5210
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 200 9026
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5207
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5205
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5209
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5204
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 200 8934
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5207
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /media/token_images/2546d54134f84345921dc76bb82ec9fd.jpg HTTP/1.1" 304 0
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5205
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 200 8927
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5200
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5210
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5206
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 200 8905
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5210
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5203
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 200 8889
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5205
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
/Users/<USER>/Project/launch/backend/tokens/models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/tokens/token/ HTTP/1.1" 200 16139
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/12/change/ HTTP/1.1" 200 35877
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/12/change/ HTTP/1.1" 200 35877
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/ HTTP/1.1" 200 16139
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/12/change/ HTTP/1.1" 200 35877
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 200 9057
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5203
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 200 9180
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5207
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5206
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5205
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5211
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /media/token_images/08f6ea55e7f748fda30dae577b8b9e9c.jpg HTTP/1.1" 304 0
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1713
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 200 8952
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5204
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 988
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5205
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5207
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5203
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5206
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5205
"GET /admin/auth/group/ HTTP/1.1" 200 9928
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/user/ HTTP/1.1" 200 13508
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/platformconfig/ HTTP/1.1" 200 15582
"GET /static/admin/img/icon-no.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/ HTTP/1.1" 200 16139
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/tokens/token/12/change/ HTTP/1.1" 200 35877
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /admin/tokens/token/12/change/ HTTP/1.1" 302 0
"GET /admin/tokens/token/ HTTP/1.1" 200 16349
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5206
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 403 58
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 403 58
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5202
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5202
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5206
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5204
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/ HTTP/1.1" 403 58
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=undefined HTTP/1.1" 403 58
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5201
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=undefined HTTP/1.1" 403 58
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=undefined HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=undefined HTTP/1.1" 403 58
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5206
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5203
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5203
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5202
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5198
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5206
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5202
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5203
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5205
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5205
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5204
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5205
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5208
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5203
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5202
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5205
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5206
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5203
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 5206
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
Forbidden: /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 403 58
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
/Users/<USER>/Project/launch/backend/tokens/models.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/ HTTP/1.1" 200 1071
"GET /media/token_images/2546d54134f84345921dc76bb82ec9fd.jpg HTTP/1.1" 304 0
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x6dc165b04121a2c5603ac991f14559e563bb51b3/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1796
"GET /media/token_images/08f6ea55e7f748fda30dae577b8b9e9c.jpg HTTP/1.1" 304 0
"GET /admin/tokens/token/ HTTP/1.1" 200 16221
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"GET /admin/tokens/token/ HTTP/1.1" 200 16221
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/ HTTP/1.1" 200 16221
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/ HTTP/1.1" 200 16221
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /admin/tokens/token/ HTTP/1.1" 200 9828
"GET /static/admin/js/cancel.js HTTP/1.1" 304 0
"POST /admin/tokens/token/ HTTP/1.1" 302 0
"GET /admin/tokens/token/ HTTP/1.1" 200 12468
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
Watching for file changes with StatReloader
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"OPTIONS /api/tokens/ HTTP/1.1" 200 0
"OPTIONS /api/tokens/ HTTP/1.1" 200 0
"POST /api/tokens/ HTTP/1.1" 201 239
Internal Server Error: /api/tokens/
"POST /api/tokens/ HTTP/1.1" 500 134
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 920
/Users/<USER>/Project/launch/backend/tokens/models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
Watching for file changes with StatReloader
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0xf74b4E81A51F4d5940c7cd355Ea82f02538826c8/ HTTP/1.1" 200 946
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
Internal Server Error: /api/tokens/
Internal Server Error: /api/tokens/
"POST /api/tokens/ HTTP/1.1" 500 93
"POST /api/tokens/ HTTP/1.1" 500 93
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
Internal Server Error: /api/tokens/
Internal Server Error: /api/tokens/
"POST /api/tokens/ HTTP/1.1" 500 93
"POST /api/tokens/ HTTP/1.1" 500 93
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"OPTIONS /api/tokens/ HTTP/1.1" 200 0
Internal Server Error: /api/tokens/
"POST /api/tokens/ HTTP/1.1" 500 93
"GET /admin/tokens/token/ HTTP/1.1" 200 15449
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/ HTTP/1.1" 200 15449
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
Internal Server Error: /admin/tokens/token/13/change/
Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 840, in get_form
    return modelform_factory(self.model, **defaults)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/forms/models.py", line 654, in modelform_factory
    return type(form)(class_name, (form,), form_class_attrs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/forms/models.py", line 334, in __new__
    raise FieldError(message)
django.core.exceptions.FieldError: Unknown field(s) (current_price, market_cap) specified for Token

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 719, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/views/decorators/cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/sites.py", line 246, in inner
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 1987, in change_view
    return self.changeform_view(request, object_id, form_url, extra_context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/utils/decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 1840, in changeform_view
    return self._changeform_view(request, object_id, form_url, extra_context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 1878, in _changeform_view
    ModelForm = self.get_form(
                ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/xlayer/lib/python3.11/site-packages/django/contrib/admin/options.py", line 842, in get_form
    raise FieldError(
django.core.exceptions.FieldError: Unknown field(s) (current_price, market_cap) specified for Token. Check fields/fieldsets/exclude attributes of class TokenAdmin.
"GET /admin/tokens/token/13/change/ HTTP/1.1" 500 163107
/Users/<USER>/Project/launch/backend/tokens/admin.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Project/launch/backend/tokens/admin.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/tokens/token/13/change/ HTTP/1.1" 200 34368
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"OPTIONS /api/tokens/ HTTP/1.1" 200 0
"POST /api/tokens/ HTTP/1.1" 201 235
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /admin/tokens/token/ HTTP/1.1" 200 15696
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 940
/Users/<USER>/Project/launch/backend/tokens/models.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
/Users/<USER>/Project/launch/backend/tokens/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cDC7a791E0d7C153b4C39EdD97f90d86dDa0E/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1523
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1523
"GET /admin/tokens/token/ HTTP/1.1" 200 15696
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/tokens/token/15/change/ HTTP/1.1" 200 34352
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
Watching for file changes with StatReloader
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"GET /admin/tokens/token/ HTTP/1.1" 200 15696
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
Watching for file changes with StatReloader
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1523
"GET /api/tokens/?show_inactive=false HTTP/1.1" 200 1523
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/price-history/?timeframe=24h HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/transaction-history/ HTTP/1.1" 200 2
"GET /api/tokens/0x7f2cdc7a791e0d7c153b4c39edd97f90d86dda0e/ HTTP/1.1" 200 910
/Users/<USER>/Project/launch/backend/tokens/urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"OPTIONS /api/users/login/ HTTP/1.1" 200 0
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"POST /api/users/login/ HTTP/1.1" 200 489
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
"POST /api/tokens/upload-image/ HTTP/1.1" 200 243
"GET /admin/tokens/token/ HTTP/1.1" 200 15696
"GET /static/admin/css/base.css HTTP/1.1" 304 0
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
"GET /static/admin/css/changelists.css HTTP/1.1" 304 0
"GET /static/admin/css/responsive.css HTTP/1.1" 304 0
"GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
"GET /static/admin/js/theme.js HTTP/1.1" 304 0
"GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
"GET /static/admin/js/core.js HTTP/1.1" 304 0
"GET /static/admin/js/actions.js HTTP/1.1" 304 0
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
"GET /static/admin/js/urlify.js HTTP/1.1" 304 0
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
"GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
"GET /static/admin/img/search.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
"GET /static/admin/js/filters.js HTTP/1.1" 304 0
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 304 0
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 304 0
"GET /admin/tokens/token/ HTTP/1.1" 200 15696
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/******************************************/ HTTP/1.1" 200 44
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"POST /api/users/login/ HTTP/1.1" 200 489
"GET /api/users/get-nonce/0x3dbEB7A2F3495f3bB969CBb03aD444B5cA0EbB4F/ HTTP/1.1" 200 44
"POST /api/users/login/ HTTP/1.1" 200 491
"GET /admin/tokens/token/ HTTP/1.1" 200 15696
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2684
"GET /api/users/get-nonce/0x87162cB0E3B0869ee7A87e739Ed444Ba8f22A07C/ HTTP/1.1" 200 44
"GET /api/users/get-nonce/0x87162cB0E3B0869ee7A87e739Ed444Ba8f22A07C/ HTTP/1.1" 200 44
"OPTIONS /api/users/login/ HTTP/1.1" 200 0
"POST /api/users/login/ HTTP/1.1" 200 491
Bad Request: /api/users/login/
"POST /api/users/login/ HTTP/1.1" 400 25
"GET /admin/users/user/ HTTP/1.1" 200 17701
"GET /static/admin/img/icon-no.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"POST /admin/users/user/ HTTP/1.1" 200 9959
"GET /static/admin/js/cancel.js HTTP/1.1" 304 0
"POST /admin/users/user/ HTTP/1.1" 302 0
"GET /admin/users/user/ HTTP/1.1" 200 15652
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/img/icon-yes.svg HTTP/1.1" 304 0
"GET /admin/tokens/token/ HTTP/1.1" 200 15696
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
