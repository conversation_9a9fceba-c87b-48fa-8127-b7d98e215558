import time
import json
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.conf import settings
from web3 import Web3
from tokens.models import Token, TokenPriceHistory, PlatformConfig
from transactions.models import Transaction

class Command(BaseCommand):
    help = 'Runs the event listener for V3 contracts'

    def handle(self, *_, **__):

        network = 'sepolia'
        config = PlatformConfig.get_current_config(network)

        provider_url = settings.WEB3_PROVIDER_URL
        if not provider_url:
            self.stderr.write('🔴 WEB3_PROVIDER_URL not set. Please check your environment variables.')
            return

        w3 = Web3(Web3.HTTPProvider(provider_url))
        if not w3.is_connected():
            self.stderr.write(f'🔴 Failed to connect to Ethereum node at {provider_url}')
            return

        self.stdout.write(f'✅ Connected to Sepolia node at {provider_url}')

        # Load ABIs
        try:
            # Correct path to ABIs from backend directory
            contracts_dir = settings.BASE_DIR.parent / 'contracts'
            with open(contracts_dir / 'artifacts/contracts/BondingCurveV3.sol/BondingCurveV3.json', 'r') as f:
                bonding_curve_abi = json.load(f)['abi']

        except FileNotFoundError as e:
            self.stderr.write(f'🔴 ABI file not found: {e}')
            return

        bonding_curve_address = Web3.to_checksum_address(config.bonding_curve_v3_address)
        bonding_curve = w3.eth.contract(address=bonding_curve_address, abi=bonding_curve_abi)

        self.stdout.write(f'🎧 Listening for events on BondingCurveV3 at {bonding_curve_address}')

        # Create filters
        purchase_filter = bonding_curve.events.TokenPurchased.create_filter(from_block='latest')
        sell_filter = bonding_curve.events.TokenSold.create_filter(from_block='latest')

        while True:
            try:
                for event in purchase_filter.get_new_entries():
                    self.handle_purchase_event(event)

                for event in sell_filter.get_new_entries():
                    self.handle_sell_event(event)

                time.sleep(10) # Poll every 10 seconds
            except Exception as e:
                self.stderr.write(f'Error polling for events: {e}')
                time.sleep(30) # Wait longer after an error

    def handle_purchase_event(self, event):
        self.stdout.write(self.style.SUCCESS(f'Purchase event found: {event.transactionHash.hex()}'))
        args = event.args
        token_address = args.token.lower()

        try:
            token = Token.objects.get(address=token_address)

            # Update token metrics
            # The event returns the new TOTAL okbCollected, not the amount from this single trade
            token.okb_collected = Decimal(args.okb_collected) / Decimal(10**18)
            # The event returns the amount of tokens for this trade
            token.tokens_traded += Decimal(args.tokens_out) / Decimal(10**18)
            token.transaction_count += 1
            token.save()

            # Create transaction record
            Transaction.objects.create(
                token=token,
                transaction_hash=event.transactionHash.hex(),
                transaction_type='BUY',
                wallet_address=args.buyer.lower(),
                token_amount=Decimal(args.tokens_out) / Decimal(10**18),
                okb_amount=Decimal(args.okb_amount) / Decimal(10**18),
                fee_amount=Decimal(args.fee) / Decimal(10**18),
                block_number=event.blockNumber,
                block_timestamp=timezone.now() # Ideally get from block, but this is simpler for now
            )

            # After saving, the token's calculated properties might have changed.
            # We must reload the token object to get the latest calculated market_cap.
            token.refresh_from_db()

            # Create price history record
            TokenPriceHistory.objects.create(
                token=token,
                timestamp=timezone.now(),
                price=Decimal(args.new_price) / Decimal(10**18),
                market_cap=token.market_cap # Now this will use the updated okb_collected value
            )
            self.stdout.write(f'✅ Processed purchase for {token.symbol}')

        except Token.DoesNotExist:
            self.stderr.write(f'⚠️ Token not found in DB: {token_address}')
        except Exception as e:
            self.stderr.write(f'🔴 Error processing purchase event: {e}')

    def handle_sell_event(self, event):
        # Similar logic for sell events
        self.stdout.write(self.style.SUCCESS(f'Sell event found: {event.transactionHash.hex()}'))
        # Implementation would be similar to handle_purchase_event
        pass
