# Generated by Django 5.2.5 on 2025-08-24 02:46

import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tokens', '0003_token_blockchain_tx_hash'),
    ]

    operations = [
        migrations.CreateModel(
            name='TokenPriceHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(db_index=True)),
                ('price', models.DecimalField(decimal_places=18, max_digits=30)),
                ('market_cap', models.DecimalField(decimal_places=18, default=Decimal('0'), max_digits=30)),
                ('network', models.CharField(choices=[('sepolia', 'Sepolia Testnet'), ('xlayer', 'X Layer'), ('mainnet', 'Ethereum Mainnet')], db_index=True, default='xlayer', max_length=20)),
                ('token', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='price_history', to='tokens.token')),
            ],
            options={
                'verbose_name': 'Token Price History',
                'verbose_name_plural': 'Token Price Histories',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['token', 'timestamp'], name='tokens_toke_token_i_3f5936_idx')],
            },
        ),
    ]
