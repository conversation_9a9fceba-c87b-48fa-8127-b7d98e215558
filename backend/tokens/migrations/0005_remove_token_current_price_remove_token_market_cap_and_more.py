# Generated by Django 5.2.5 on 2025-08-24 11:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tokens', '0004_tokenpricehistory'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='token',
            name='current_price',
        ),
        migrations.RemoveField(
            model_name='token',
            name='market_cap',
        ),
        migrations.AlterField(
            model_name='token',
            name='phase',
            field=models.CharField(choices=[('PENDING', 'Pending Initial Purchase'), ('CREATED', 'Created - Ready for Curve Trading'), ('CURVE', 'Curve Trading Active'), ('GRADUATING', 'Graduating to DEX'), ('GRADUATED', 'Graduated to DEX')], db_index=True, default='CREATED', max_length=20),
        ),
    ]
