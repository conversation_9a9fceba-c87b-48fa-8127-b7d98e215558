from rest_framework import serializers
from .models import Token, TokenPriceHistory

class TokenPriceHistorySerializer(serializers.ModelSerializer):
    """Serializer for the TokenPriceHistory model."""
    class Meta:
        model = TokenPriceHistory
        fields = ['timestamp', 'price', 'market_cap']

class TokenSerializer(serializers.ModelSerializer):
    """Serializer for the Token model."""
    class Meta:
        model = Token
        fields = '__all__'

