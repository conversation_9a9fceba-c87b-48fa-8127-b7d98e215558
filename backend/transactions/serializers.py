from rest_framework import serializers
from .models import Transaction

class TransactionSerializer(serializers.ModelSerializer):
    """Serializer for the Transaction model."""
    # Use block_timestamp as timestamp for the frontend
    timestamp = serializers.DateTimeField(source='block_timestamp', read_only=True)

    class Meta:
        model = Transaction
        fields = [
            'transaction_hash',
            'user_address',
            'transaction_type',
            'token_amount',
            'okb_amount',
            'price',
            'timestamp' # Use the aliased field
        ]

