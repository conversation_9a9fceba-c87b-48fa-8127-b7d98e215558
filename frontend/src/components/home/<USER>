'use client';

import { useState, useEffect } from 'react';
import { TokenCard } from './TokenCard';
import Link from 'next/link';

interface Token {
  address: string;
  name: string;
  symbol: string;
  phase: string;
  price?: number;
  change?: number;
  market_cap?: number;
  volume_24h?: number;
  progress?: number;
  creator_address?: string;
  creator_name?: string;
  creator_verified?: boolean;
  image_url?: string;
  description?: string;
  okb_collected?: string;
}

export function TokenList() {
  const [activeTab, setActiveTab] = useState('New');
  const [tokens, setTokens] = useState<Token[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const tabs = ['New', 'Live', 'Graduated'];

  // 获取真实token数据
  useEffect(() => {
    const fetchTokens = async () => {
      try {
        setIsLoading(true);
        const apiUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';
        const response = await fetch(`${apiUrl}/api/tokens/`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch tokens: ${response.status}`);
        }
        
        const result = await response.json();
        console.log('API response:', result); // 调试信息
        
        if (result.success) {
          // 根据后端API的实际结构，tokens在result.data.tokens中
          let tokenArray = [];
          
          if (result.data && result.data.tokens && Array.isArray(result.data.tokens)) {
            // 正确的数据结构：result.data.tokens
            tokenArray = result.data.tokens;
          } else if (Array.isArray(result.data)) {
            // 备用：直接检查result.data是否为数组
            tokenArray = result.data;
          } else if (Array.isArray(result.tokens)) {
            // 备用：直接检查result.tokens
            tokenArray = result.tokens;
          } else {
            console.error('Unexpected data structure:', result);
            throw new Error('Invalid data format: no token array found');
          }
          
          console.log('Found token array:', tokenArray);
          
          // 处理token数据，添加默认值
          const processedTokens = tokenArray.map((token: any) => ({
            address: token.address,
            name: token.name,
            symbol: token.symbol,
            phase: token.phase || 'NEW',
            price: parseFloat(token.currentPrice || token.price || '0'),
            change: parseFloat(token.priceChange24h || token.change_24h || '0'),
            market_cap: parseFloat(token.marketCap || token.market_cap || '0'),
            volume_24h: parseFloat(token.volume24h || token.volume_24h || '0'),
            progress: token.phase === 'LIVE' ? 
              Math.min((parseFloat(token.okb_collected || '0') / 200) * 100, 100) : 
              token.phase === 'GRADUATED' ? 100 : 0,
            creator_address: token.creator,
            creator_name: token.creator_name || 'Anonymous',
            creator_verified: token.isVerified || token.creator_verified || false,
            image_url: token.imageUrl || token.image_url,
            description: token.description,
            okb_collected: token.okb_collected || '0'
          }));
          
          console.log('Processed tokens:', processedTokens);
          setTokens(processedTokens);
        } else {
          throw new Error(result.error || 'Failed to fetch tokens');
        }
      } catch (err) {
        console.error('Error fetching tokens:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch tokens');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTokens();
  }, []);

  // 根据tab过滤token
  const filteredTokens = tokens.filter(token => {
    switch (activeTab) {
      case 'New':
        return token.phase === 'CREATED' || token.phase === 'CURVE' || token.phase === 'NEW';
      case 'Live':
        return token.phase === 'LIVE' || token.phase === 'TRADING';
      case 'Graduated':
        return token.phase === 'GRADUATED';
      default:
        return true;
    }
  });

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        <p className="text-gray-400 mt-4">Loading tokens...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-400 mb-4">Error: {error}</p>
        <button 
          onClick={() => window.location.reload()} 
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
        >
          Retry
        </button>
      </div>
    );
  }

  if (filteredTokens.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-400">No tokens found in the {activeTab.toLowerCase()} category.</p>
      </div>
    );
  }

  return (
    <div>
      {/* 调试信息（开发环境） */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mb-4 p-4 bg-gray-800 rounded border border-gray-600">
          <h3 className="text-sm font-mono text-gray-300 mb-2">Debug Info:</h3>
          <div className="text-xs text-gray-400 space-y-1">
            <div>Total tokens: {tokens.length}</div>
            <div>Filtered tokens: {filteredTokens.length}</div>
            <div>Active tab: {activeTab}</div>
            <div>API URL: {process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'}</div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="flex justify-center space-x-4 border-b border-gray-800 mb-8">
        {tabs.map(tab => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`py-2 px-4 font-semibold transition-colors duration-200 ${
              activeTab === tab
                ? 'text-white border-b-2 border-blue-500'
                : 'text-gray-500 hover:text-white'
            }`}>
            {tab}
          </button>
        ))}
      </div>

      {/* Token Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTokens.map((token) => (
          <Link key={token.address} href={`/token/${token.address}`}>
            <TokenCard token={token} />
          </Link>
        ))}
      </div>
    </div>
  );
}
