'use client';

import { motion } from 'framer-motion';

const phaseColors: { [key: string]: string } = {
  CREATED: 'bg-green-500',
  CURVE: 'bg-blue-500',
  LIVE: 'bg-blue-500',
  GRADUATED: 'bg-purple-500',
  NEW: 'bg-green-500',
};

const phaseLabels: { [key: string]: string } = {
  CREATED: 'NEW',
  CURVE: 'NEW',
  LIVE: 'LIVE',
  GRADUATED: 'GRADUATED',
  NEW: 'NEW',
};

export function TokenCard({ token }: { token: any }) {
  const formatPrice = (price: number) => {
    if (price === 0) return '$0.00';
    if (price < 0.0001) return `$${price.toExponential(4)}`;
    if (price < 0.01) return `$${price.toFixed(6)}`;
    if (price < 1) return `$${price.toFixed(4)}`;
    return `$${price.toFixed(2)}`;
  };

  const formatMarketCap = (marketCap: number) => {
    if (marketCap === 0) return '$0';
    if (marketCap >= 1e9) return `$${(marketCap / 1e9).toFixed(2)}B`;
    if (marketCap >= 1e6) return `$${(marketCap / 1e6).toFixed(2)}M`;
    if (marketCap >= 1e3) return `$${(marketCap / 1e3).toFixed(2)}K`;
    return `$${marketCap.toFixed(2)}`;
  };

  const formatVolume = (volume: number) => {
    if (volume === 0) return '$0';
    if (volume >= 1e9) return `$${(volume / 1e9).toFixed(2)}B`;
    if (volume >= 1e6) return `$${(volume / 1e6).toFixed(2)}M`;
    if (volume >= 1e3) return `$${(volume / 1e3).toFixed(2)}K`;
    return `$${volume.toFixed(2)}`;
  };

  return (
    <motion.div
      whileHover={{ scale: 1.03 }}
      className="bg-gray-900 rounded-lg p-6 border border-gray-800 hover:border-gray-700 transition-colors duration-200 cursor-pointer"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gray-700 rounded-full overflow-hidden">
            {token.image_url ? (
              <img 
                src={token.image_url} 
                alt={token.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <span className="text-white font-bold text-sm">
                  {token.symbol.slice(0, 2).toUpperCase()}
                </span>
              </div>
            )}
          </div>
          <div>
            <h3 className="font-bold text-lg">{token.name}</h3>
            <p className="text-gray-400 text-sm">${token.symbol}</p>
          </div>
        </div>
        <div className={`text-xs font-bold px-2 py-1 rounded-full ${phaseColors[token.phase] || 'bg-gray-500'}`}>
          {phaseLabels[token.phase] || token.phase}
        </div>
      </div>

      <div className="my-4">
        <p className="text-3xl font-bold">{formatPrice(token.price || 0)}</p>
        <p className={`text-sm ${(token.change || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
          {(token.change || 0) >= 0 ? '+' : ''}{(token.change || 0).toFixed(2)}%
        </p>
      </div>

      {token.phase === 'LIVE' && token.progress !== undefined && (
        <div className="my-4">
          <div className="w-full bg-gray-700 rounded-full h-2.5">
            <div className="bg-blue-500 h-2.5 rounded-full" style={{ width: `${Math.min(token.progress, 100)}%` }}></div>
          </div>
          <p className="text-xs text-gray-400 mt-1 text-right">
            {token.okb_collected ? `${parseFloat(token.okb_collected).toFixed(2)} / 200 OKB` : 'Graduation Progress'}
          </p>
        </div>
      )}

      <div className="text-sm text-gray-400 space-y-2">
        <div className="flex justify-between">
          <span>Market Cap</span>
          <span>{formatMarketCap(token.market_cap || 0)}</span>
        </div>
        <div className="flex justify-between">
          <span>Volume (24h)</span>
          <span>{formatVolume(token.volume_24h || 0)}</span>
        </div>
      </div>

      <div className="border-t border-gray-800 mt-4 pt-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center">
            <span className="text-xs text-white">
              {token.creator_name ? token.creator_name.charAt(0).toUpperCase() : 'A'}
            </span>
          </div>
          <span className="text-xs">{token.creator_name || 'Anonymous'}</span>
          {token.creator_verified && <span className="text-blue-400 text-xs">✓</span>}
        </div>
      </div>
    </motion.div>
  );
}
