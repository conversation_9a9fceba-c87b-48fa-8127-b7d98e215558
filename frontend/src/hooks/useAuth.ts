import { useEffect, useState } from 'react';
import { useAccount, useSignMessage } from 'wagmi';

// 使用正确的环境变量，并提供默认值
const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';
const JWT_KEY = 'jwt_token';

export function useAuth() {
  const { address, isConnected } = useAccount();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const { signMessageAsync } = useSignMessage();

  useEffect(() => {
    const token = localStorage.getItem(JWT_KEY);
    if (token) {
      // TODO: Add token validation logic here
      setIsAuthenticated(true);
    } else {
      setIsAuthenticated(false);
    }
  }, [isConnected, address]);

  useEffect(() => {
    const login = async () => {
      if (isConnected && address && !localStorage.getItem(JWT_KEY)) {
        try {
          console.log('Attempting to authenticate with address:', address);
          console.log('Using API URL:', API_URL);
          
          // 1. Get nonce from backend
          const nonceUrl = `${API_URL}/api/users/get-nonce/${address}/`;
          console.log('Fetching nonce from:', nonceUrl);
          
          const nonceRes = await fetch(nonceUrl);
          
          if (!nonceRes.ok) {
            throw new Error(`Failed to get nonce: ${nonceRes.status} ${nonceRes.statusText}`);
          }
          
          const { nonce } = await nonceRes.json();
          console.log('Received nonce:', nonce);

          // 2. Sign the nonce
          const messageToSign = `Please sign this message to log in: ${nonce}`;
          const signature = await signMessageAsync({ message: messageToSign });
          console.log('Message signed successfully');

          // 3. Send signature to backend to log in
          const loginUrl = `${API_URL}/api/users/login/`;
          console.log('Sending login request to:', loginUrl);
          
          const loginRes = await fetch(loginUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ address, signature, nonce }),
          });

          if (!loginRes.ok) {
            const errorText = await loginRes.text();
            throw new Error(`Login failed: ${loginRes.status} ${loginRes.statusText} - ${errorText}`);
          }

          const { token } = await loginRes.json();

          // 4. Store JWT
          localStorage.setItem(JWT_KEY, token);
          setIsAuthenticated(true);
          console.log('Login successful');

        } catch (error) {
          console.error('Authentication error:', error);
          
          // 提供更详细的错误信息
          const errorMessage = error instanceof Error ? error.message : String(error);
          
          if (errorMessage.includes('Failed to fetch')) {
            console.error('Network error - cannot connect to backend server');
            console.error('Please ensure the backend is running on:', API_URL);
          } else if (errorMessage.includes('Failed to get nonce')) {
            console.error('Backend error - nonce endpoint not responding');
          } else if (errorMessage.includes('Login failed')) {
            console.error('Backend error - login endpoint not responding');
          }
          
          // Clear any stale token if login fails
          localStorage.removeItem(JWT_KEY);
          setIsAuthenticated(false);
        }
      }
    };

    login();
  }, [isConnected, address, signMessageAsync]);

  useEffect(() => {
    const handleAccountChange = () => {
      const token = localStorage.getItem(JWT_KEY);
      if (token) {
        console.log('Wallet account changed, logging out.');
        logout();
      }
    };

    window.addEventListener('accountChanged', handleAccountChange);

    // On initial load, if address is different from what we might have stored,
    // logout.
    handleAccountChange();

    return () => {
      window.removeEventListener('accountChanged', handleAccountChange);
    };
  }, [address]);

  const logout = () => {
    localStorage.removeItem(JWT_KEY);
    setIsAuthenticated(false);
    console.log('Logged out');
  };

  return { isAuthenticated, logout };
}

